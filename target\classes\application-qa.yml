# QA环境配置
spring:
  # Redis配置
  data:
    redis:
      host: r-2zebv0kv0ctz3dbopy.redis.rds.aliyuncs.com
      port: 6379
      password: 1KEMnJdiAcbpLucW
      database: 8
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

# 服务器配置
server:
  port: 8080

# 日志配置
logging:
  level:
    com.gwm.ailab.service: DEBUG
    org.springframework.web: DEBUG
  file:
    name: logs/generative-card-service-qa.log

# 自定义配置
app:
  config:
    environment: qa
    debug: true
    external-api:
      base-url: http://qa-api.example.com
      timeout: 30000
      retry-count: 3
