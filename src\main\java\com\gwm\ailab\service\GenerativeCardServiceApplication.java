package com.gwm.ailab.service;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Generative Card Service Application
 * 
 * <AUTHOR> Lab
 * @version 1.0.0
 */
@SpringBootApplication
@EnableAspectJAutoProxy
@EnableDiscoveryClient
@EnableAsync
public class GenerativeCardServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(GenerativeCardServiceApplication.class, args);
    }
}
