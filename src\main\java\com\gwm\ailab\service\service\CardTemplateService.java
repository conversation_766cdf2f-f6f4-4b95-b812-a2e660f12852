package com.gwm.ailab.service.service;

import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * 卡片服务类
 *
 * <AUTHOR> Lab
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CardTemplateService {

    private final RedisTemplate<String, String> redisTemplate;


    public void syncTemplateToRedis(JSONObject json) {
        redisTemplate.opsForValue().set("service:card:template:sync:" + json.getString("code"), json.getString("content"));
        log.warn("同步模板, key:【{}】, value:【{}】", json.getString("code"), json.getString("content"));
    }
}
