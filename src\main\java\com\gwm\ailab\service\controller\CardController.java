package com.gwm.ailab.service.controller;

import com.alibaba.fastjson2.JSONObject;
import com.gwm.ailab.service.common.ResponseResult;
import com.gwm.ailab.service.service.CardTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 模板控制器
 *
 * <AUTHOR> Lab
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/template")
@Validated
@Tag(name = "服务卡片模板管理", description = "卡片模板相关的API接口")
@RequiredArgsConstructor
public class CardController {

    private final CardTemplateService cardTemplateService;

    @PostMapping("/manual/sync")
    @Operation(summary = "服务卡片同步", description = "服务卡片同步")
    @ApiResponse(description = "服务卡片同步", content = @Content(mediaType = "application/json"))
    public ResponseResult<Void> templateSync(@RequestBody JSONObject json) {
        cardTemplateService.syncTemplateToRedis(json);
        return ResponseResult.success();
    }


}
