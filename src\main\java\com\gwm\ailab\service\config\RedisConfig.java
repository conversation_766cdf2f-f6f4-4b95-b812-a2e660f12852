package com.gwm.ailab.service.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * Redis配置类
 *
 * <AUTHOR> Lab
 * @version 1.0.0
 */
@Slf4j
@Configuration
public class RedisConfig {

    @Bean
    public RedisTemplate<String, String> redisTemplate( ) {
        return new RedisTemplate<>();
    }

}
