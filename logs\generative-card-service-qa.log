2025-08-26 11:35:20 [main] INFO  c.g.a.s.GenerativeCardServiceApplication - Starting GenerativeCardServiceApplication using Java 17.0.12 with PID 36068 (D:\dev-center-dataservice\generative-card-service\target\classes started by ********** in D:\dev-center-dataservice\generative-card-service)
2025-08-26 11:35:20 [main] DEBUG c.g.a.s.GenerativeCardServiceApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-08-26 11:35:20 [main] INFO  c.g.a.s.GenerativeCardServiceApplication - The following 1 profile is active: "qa"
2025-08-26 11:35:22 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-26 11:35:22 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-26 11:35:22 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-08-26 11:35:24 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-26 11:35:24 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-26 11:35:24 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-26 11:35:24 [main] INFO  o.a.c.c.C.[.[.[/generative-card] - Initializing Spring embedded WebApplicationContext
2025-08-26 11:35:24 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4165 ms
2025-08-26 11:35:24 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-08-26 11:35:26 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 7 mappings in 'requestMappingHandlerMapping'
2025-08-26 11:35:26 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-08-26 11:35:26 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-08-26 11:35:26 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-08-26 11:35:27 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-08-26 11:35:28 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/generative-card'
2025-08-26 11:35:28 [main] INFO  c.g.a.s.GenerativeCardServiceApplication - Started GenerativeCardServiceApplication in 8.935 seconds (process running for 11.712)
2025-08-26 11:38:30 [main] INFO  c.g.a.s.GenerativeCardServiceApplication - Starting GenerativeCardServiceApplication using Java 17.0.12 with PID 28300 (D:\dev-center-dataservice\generative-card-service\target\classes started by ********** in D:\dev-center-dataservice\generative-card-service)
2025-08-26 11:38:30 [main] DEBUG c.g.a.s.GenerativeCardServiceApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-08-26 11:38:30 [main] INFO  c.g.a.s.GenerativeCardServiceApplication - The following 1 profile is active: "qa"
2025-08-26 11:38:33 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-26 11:38:33 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-26 11:38:33 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-08-26 11:38:34 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-26 11:38:34 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-26 11:38:34 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-26 11:38:34 [main] INFO  o.a.c.c.C.[.[.[/generative-card] - Initializing Spring embedded WebApplicationContext
2025-08-26 11:38:34 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3838 ms
2025-08-26 11:38:35 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-08-26 11:38:36 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 7 mappings in 'requestMappingHandlerMapping'
2025-08-26 11:38:36 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-08-26 11:38:36 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-08-26 11:38:36 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-08-26 11:38:37 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-08-26 11:38:37 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/generative-card'
2025-08-26 11:38:37 [main] INFO  c.g.a.s.GenerativeCardServiceApplication - Started GenerativeCardServiceApplication in 8.144 seconds (process running for 10.863)
2025-08-26 11:39:57 [http-nio-8080-exec-9] INFO  o.a.c.c.C.[.[.[/generative-card] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-26 11:39:57 [http-nio-8080-exec-9] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-26 11:39:57 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-08-26 11:39:57 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-08-26 11:39:57 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-08-26 11:39:57 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@2dee6f8
2025-08-26 11:39:57 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@72f63197
2025-08-26 11:39:57 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-08-26 11:39:57 [http-nio-8080-exec-9] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-08-26 11:39:57 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/cards/health", parameters={}
2025-08-26 11:39:57 [http-nio-8080-exec-9] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.gwm.ailab.service.controller.CardController#health()
2025-08-26 11:39:57 [http-nio-8080-exec-9] INFO  c.g.a.service.aspect.ApiLogAspect - === API Request Start ===
2025-08-26 11:39:57 [http-nio-8080-exec-9] INFO  c.g.a.service.aspect.ApiLogAspect - Request Info: {"className":"CardController","executionTime":0,"headers":{"host":"localhost:8080","connection":"Keep-Alive","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.6093"},"method":"GET","methodName":"health","remoteAddr":"0:0:0:0:0:0:0:1","success":false,"uri":"/generative-card/cards/health","url":"http://localhost:8080/generative-card/cards/health","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.6093"}
2025-08-26 11:39:57 [http-nio-8080-exec-9] INFO  c.g.a.service.aspect.ApiLogAspect - === API Request Success ===
2025-08-26 11:39:57 [http-nio-8080-exec-9] INFO  c.g.a.service.aspect.ApiLogAspect - Response Info: {"className":"CardController","executionTime":282,"headers":{"host":"localhost:8080","connection":"Keep-Alive","user-agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.6093"},"method":"GET","methodName":"health","remoteAddr":"0:0:0:0:0:0:0:1","result":"{\"code\":200,\"data\":\"OK\",\"message\":\"服务运行正常\",\"timestamp\":\"2025-08-26 11:39:57.607746100\"}","success":true,"uri":"/generative-card/cards/health","url":"http://localhost:8080/generative-card/cards/health","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.6093"}
2025-08-26 11:39:57 [http-nio-8080-exec-9] INFO  c.g.a.service.aspect.ApiLogAspect - === API Request End (282ms) ===
2025-08-26 11:39:57 [http-nio-8080-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-08-26 11:39:57 [http-nio-8080-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [ResponseResult(code=200, message=服务运行正常, data=OK, timestamp=2025-08-26T11:39:57.607746100)]
2025-08-26 11:39:57 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 11:40:06 [http-nio-8080-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/swagger-ui.html", parameters={}
2025-08-26 11:40:06 [http-nio-8080-exec-8] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to org.springdoc.webmvc.ui.SwaggerWelcomeWebMvc#redirectToUi(HttpServletRequest)
2025-08-26 11:40:06 [http-nio-8080-exec-8] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-08-26 11:40:06 [http-nio-8080-exec-8] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Nothing to write: null body
2025-08-26 11:40:06 [http-nio-8080-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - Completed 302 FOUND
2025-08-26 11:40:06 [http-nio-8080-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/swagger-ui/index.html", parameters={}
2025-08-26 11:40:06 [http-nio-8080-exec-5] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]]
2025-08-26 11:40:06 [http-nio-8080-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 11:40:19 [http-nio-8080-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/api-docs", parameters={}
2025-08-26 11:40:19 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to org.springdoc.webmvc.api.OpenApiWebMvcResource#openapiJson(HttpServletRequest, String, Locale)
2025-08-26 11:40:19 [http-nio-8080-exec-7] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 335 ms
2025-08-26 11:40:19 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json]
2025-08-26 11:40:19 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{123, 34, 111, 112, 101, 110, 97, 112, 105, 34, 58, 34, 51, 46, 48, 46, 49, 34, 44, 34, 105, 110, 10 (truncated)...]
2025-08-26 11:40:19 [http-nio-8080-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 11:40:29 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/swagger-ui.html", parameters={}
2025-08-26 11:40:29 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to org.springdoc.webmvc.ui.SwaggerWelcomeWebMvc#redirectToUi(HttpServletRequest)
2025-08-26 11:40:29 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json;q=0.8', given [text/html, application/xhtml+xml, image/avif, image/webp, image/apng, application/xml;q=0.9, */*;q=0.8, application/signed-exchange;v=b3;q=0.7] and supported [application/json, application/*+json]
2025-08-26 11:40:29 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Nothing to write: null body
2025-08-26 11:40:29 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 302 FOUND
2025-08-26 11:40:29 [http-nio-8080-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/swagger-ui/index.html", parameters={}
2025-08-26 11:40:29 [http-nio-8080-exec-3] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]]
2025-08-26 11:40:29 [http-nio-8080-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 11:40:30 [http-nio-8080-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/swagger-ui/swagger-ui.css", parameters={}
2025-08-26 11:40:30 [http-nio-8080-exec-4] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]]
2025-08-26 11:40:30 [http-nio-8080-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 11:40:30 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/swagger-ui/swagger-ui-bundle.js", parameters={}
2025-08-26 11:40:30 [http-nio-8080-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/swagger-ui/index.css", parameters={}
2025-08-26 11:40:30 [http-nio-8080-exec-9] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]]
2025-08-26 11:40:30 [http-nio-8080-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/swagger-ui/swagger-ui-standalone-preset.js", parameters={}
2025-08-26 11:40:30 [http-nio-8080-exec-6] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]]
2025-08-26 11:40:30 [http-nio-8080-exec-8] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]]
2025-08-26 11:40:30 [http-nio-8080-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/swagger-ui/swagger-initializer.js", parameters={}
2025-08-26 11:40:30 [http-nio-8080-exec-5] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]]
2025-08-26 11:40:30 [http-nio-8080-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 11:40:30 [http-nio-8080-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 11:40:30 [http-nio-8080-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 11:40:30 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 11:40:31 [http-nio-8080-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/api-docs/swagger-config", parameters={}
2025-08-26 11:40:31 [http-nio-8080-exec-10] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to org.springdoc.webmvc.ui.SwaggerConfigResource#openapiJson(HttpServletRequest)
2025-08-26 11:40:31 [http-nio-8080-exec-10] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json]
2025-08-26 11:40:31 [http-nio-8080-exec-10] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{configUrl=/generative-card/api-docs/swagger-config, oauth2RedirectUrl=http://localhost:8080/generat (truncated)...]
2025-08-26 11:40:31 [http-nio-8080-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 11:40:31 [http-nio-8080-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/swagger-ui/favicon-32x32.png", parameters={}
2025-08-26 11:40:31 [http-nio-8080-exec-7] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]]
2025-08-26 11:40:31 [http-nio-8080-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 11:40:31 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/api-docs", parameters={}
2025-08-26 11:40:31 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to org.springdoc.webmvc.api.OpenApiWebMvcResource#openapiJson(HttpServletRequest, String, Locale)
2025-08-26 11:40:31 [http-nio-8080-exec-1] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 81 ms
2025-08-26 11:40:31 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, */*] and supported [application/json]
2025-08-26 11:40:31 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{123, 34, 111, 112, 101, 110, 97, 112, 105, 34, 58, 34, 51, 46, 48, 46, 49, 34, 44, 34, 105, 110, 10 (truncated)...]
2025-08-26 11:40:31 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 11:41:15 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/swagger-ui/index.html", parameters={}
2025-08-26 11:41:15 [http-nio-8080-exec-2] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]]
2025-08-26 11:41:15 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 11:41:15 [http-nio-8080-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/swagger-ui/swagger-ui.css", parameters={}
2025-08-26 11:41:15 [http-nio-8080-exec-3] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]]
2025-08-26 11:41:15 [http-nio-8080-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/swagger-ui/index.css", parameters={}
2025-08-26 11:41:15 [http-nio-8080-exec-4] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]]
2025-08-26 11:41:15 [http-nio-8080-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 11:41:15 [http-nio-8080-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 11:41:15 [http-nio-8080-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/swagger-ui/swagger-ui-standalone-preset.js", parameters={}
2025-08-26 11:41:15 [http-nio-8080-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/swagger-ui/swagger-ui-bundle.js", parameters={}
2025-08-26 11:41:15 [http-nio-8080-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/swagger-ui/swagger-initializer.js", parameters={}
2025-08-26 11:41:15 [http-nio-8080-exec-8] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]]
2025-08-26 11:41:15 [http-nio-8080-exec-6] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]]
2025-08-26 11:41:15 [http-nio-8080-exec-5] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]]
2025-08-26 11:41:15 [http-nio-8080-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 11:41:15 [http-nio-8080-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 11:41:15 [http-nio-8080-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 11:41:15 [http-nio-8080-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/api-docs/swagger-config", parameters={}
2025-08-26 11:41:15 [http-nio-8080-exec-10] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to org.springdoc.webmvc.ui.SwaggerConfigResource#openapiJson(HttpServletRequest)
2025-08-26 11:41:15 [http-nio-8080-exec-10] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json]
2025-08-26 11:41:15 [http-nio-8080-exec-10] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{configUrl=/generative-card/api-docs/swagger-config, oauth2RedirectUrl=http://localhost:8080/generat (truncated)...]
2025-08-26 11:41:15 [http-nio-8080-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 11:41:15 [http-nio-8080-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/swagger-ui/favicon-32x32.png", parameters={}
2025-08-26 11:41:15 [http-nio-8080-exec-7] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]]
2025-08-26 11:41:15 [http-nio-8080-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 11:41:15 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/api-docs", parameters={}
2025-08-26 11:41:15 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to org.springdoc.webmvc.api.OpenApiWebMvcResource#openapiJson(HttpServletRequest, String, Locale)
2025-08-26 11:41:15 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, */*] and supported [application/json]
2025-08-26 11:41:15 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{123, 34, 111, 112, 101, 110, 97, 112, 105, 34, 58, 34, 51, 46, 48, 46, 49, 34, 44, 34, 105, 110, 10 (truncated)...]
2025-08-26 11:41:15 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 11:41:23 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - GET "/generative-card/cards/health", parameters={}
2025-08-26 11:41:23 [http-nio-8080-exec-9] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.gwm.ailab.service.controller.CardController#health()
2025-08-26 11:41:23 [http-nio-8080-exec-9] INFO  c.g.a.service.aspect.ApiLogAspect - === API Request Start ===
2025-08-26 11:41:23 [http-nio-8080-exec-9] INFO  c.g.a.service.aspect.ApiLogAspect - Request Info: {"className":"CardController","executionTime":0,"headers":{"sec-fetch-mode":"cors","referer":"http://localhost:8080/generative-card/swagger-ui/index.html","sec-fetch-site":"same-origin","accept-language":"zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6","accept":"*/*","sec-ch-ua":"\"Not;A=Brand\";v=\"99\", \"Microsoft Edge\";v=\"139\", \"Chromium\";v=\"139\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"localhost:8080","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","sec-fetch-dest":"empty"},"method":"GET","methodName":"health","referer":"http://localhost:8080/generative-card/swagger-ui/index.html","remoteAddr":"0:0:0:0:0:0:0:1","success":false,"uri":"/generative-card/cards/health","url":"http://localhost:8080/generative-card/cards/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
2025-08-26 11:41:23 [http-nio-8080-exec-9] INFO  c.g.a.service.aspect.ApiLogAspect - === API Request Success ===
2025-08-26 11:41:23 [http-nio-8080-exec-9] INFO  c.g.a.service.aspect.ApiLogAspect - Response Info: {"className":"CardController","executionTime":0,"headers":{"sec-fetch-mode":"cors","referer":"http://localhost:8080/generative-card/swagger-ui/index.html","sec-fetch-site":"same-origin","accept-language":"zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6","accept":"*/*","sec-ch-ua":"\"Not;A=Brand\";v=\"99\", \"Microsoft Edge\";v=\"139\", \"Chromium\";v=\"139\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"localhost:8080","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","sec-fetch-dest":"empty"},"method":"GET","methodName":"health","referer":"http://localhost:8080/generative-card/swagger-ui/index.html","remoteAddr":"0:0:0:0:0:0:0:1","result":"{\"code\":200,\"data\":\"OK\",\"message\":\"服务运行正常\",\"timestamp\":\"2025-08-26 11:41:23.154246800\"}","success":true,"uri":"/generative-card/cards/health","url":"http://localhost:8080/generative-card/cards/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
2025-08-26 11:41:23 [http-nio-8080-exec-9] INFO  c.g.a.service.aspect.ApiLogAspect - === API Request End (0ms) ===
2025-08-26 11:41:23 [http-nio-8080-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-08-26 11:41:23 [http-nio-8080-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [ResponseResult(code=200, message=服务运行正常, data=OK, timestamp=2025-08-26T11:41:23.154246800)]
2025-08-26 11:41:23 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-26 14:43:31 [main] WARN  c.a.n.client.logging.NacosLogging - Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
2025-08-26 14:43:31 [main] WARN  c.a.n.client.logging.NacosLogging - Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
2025-08-26 14:43:31 [main] INFO  c.g.a.s.GenerativeCardServiceApplication - Starting GenerativeCardServiceApplication using Java 17.0.12 with PID 35856 (D:\dev-center-dataservice\generative-card-service\target\classes started by ********** in D:\dev-center-dataservice\generative-card-service)
2025-08-26 14:43:31 [main] DEBUG c.g.a.s.GenerativeCardServiceApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-08-26 14:43:31 [main] INFO  c.g.a.s.GenerativeCardServiceApplication - The following 1 profile is active: "qa"
2025-08-26 14:43:33 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-26 14:43:33 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-26 14:43:33 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-08-26 14:43:33 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=b74fa441-84b3-3e21-8ecb-a388fd625e05
2025-08-26 14:43:34 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-26 14:43:34 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-26 14:43:34 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-26 14:43:34 [main] INFO  o.a.c.c.C.[.[.[/generative-card] - Initializing Spring embedded WebApplicationContext
2025-08-26 14:43:34 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2605 ms
2025-08-26 14:43:34 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-08-26 14:43:34 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cardController' defined in file [D:\dev-center-dataservice\generative-card-service\target\classes\com\gwm\ailab\service\controller\CardController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'cardTemplateService' defined in file [D:\dev-center-dataservice\generative-card-service\target\classes\com\gwm\ailab\service\service\CardTemplateService.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'org.springframework.data.redis.core.RedisTemplate<java.lang.String, java.lang.String>' available: expected single matching bean but found 2: redisTemplate,stringRedisTemplate
2025-08-26 14:43:34 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-26 14:43:34 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-26 14:43:34 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.gwm.ailab.service.service.CardTemplateService required a single bean, but 2 were found:
	- redisTemplate: defined by method 'redisTemplate' in class path resource [com/gwm/ailab/service/config/RedisConfig.class]
	- stringRedisTemplate: defined by method 'stringRedisTemplate' in class path resource [org/springframework/boot/autoconfigure/data/redis/RedisAutoConfiguration.class]


Action:

Consider marking one of the beans as @Primary, updating the consumer to accept multiple beans, or using @Qualifier to identify the bean that should be consumed

2025-08-26 14:46:32 [main] WARN  c.a.n.client.logging.NacosLogging - Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
2025-08-26 14:46:32 [main] WARN  c.a.n.client.logging.NacosLogging - Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
2025-08-26 14:46:32 [main] INFO  c.g.a.s.GenerativeCardServiceApplication - Starting GenerativeCardServiceApplication using Java 17.0.12 with PID 33472 (D:\dev-center-dataservice\generative-card-service\target\classes started by ********** in D:\dev-center-dataservice\generative-card-service)
2025-08-26 14:46:32 [main] DEBUG c.g.a.s.GenerativeCardServiceApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-08-26 14:46:32 [main] INFO  c.g.a.s.GenerativeCardServiceApplication - The following 1 profile is active: "qa"
2025-08-26 14:46:34 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-26 14:46:34 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-26 14:46:34 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-08-26 14:46:34 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=7e3f4abe-74e6-31f9-ad8d-1af55bb228f2
2025-08-26 14:46:35 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-26 14:46:35 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-26 14:46:35 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-26 14:46:35 [main] INFO  o.a.c.c.C.[.[.[/generative-card] - Initializing Spring embedded WebApplicationContext
2025-08-26 14:46:35 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2384 ms
2025-08-26 14:46:35 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-08-26 14:46:36 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 7 mappings in 'requestMappingHandlerMapping'
2025-08-26 14:46:36 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-08-26 14:46:36 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-08-26 14:46:36 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-08-26 14:46:38 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-26 14:46:40 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-26 14:46:40 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'compositeCompatibilityVerifier' defined in class path resource [org/springframework/cloud/configuration/CompatibilityVerifierAutoConfiguration.class]: Failed to instantiate [org.springframework.cloud.configuration.CompositeCompatibilityVerifier]: Factory method 'compositeCompatibilityVerifier' threw exception with message: Spring Cloud/ Spring Boot version compatibility checks have failed: [[VerificationResult@6a358906 description = 'Spring Boot [3.2.0] is not compatible with this Spring Cloud release train', action = 'Change Spring Boot version to one of the following versions [3.0.x] .
You can find the latest Spring Boot versions here [https://spring.io/projects/spring-boot#learn]. 
If you want to learn more about the Spring Cloud Release train compatibility, you can visit this page [https://spring.io/projects/spring-cloud#overview] and check the [Release Trains] section.
If you want to disable this check, just set the property [spring.cloud.compatibility-verifier.enabled=false]']]
2025-08-26 14:46:40 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-26 14:46:40 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-26 14:46:40 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Your project setup is incompatible with our requirements due to following reasons:

- Spring Boot [3.2.0] is not compatible with this Spring Cloud release train


Action:

Consider applying the following actions:

- Change Spring Boot version to one of the following versions [3.0.x] .
You can find the latest Spring Boot versions here [https://spring.io/projects/spring-boot#learn]. 
If you want to learn more about the Spring Cloud Release train compatibility, you can visit this page [https://spring.io/projects/spring-cloud#overview] and check the [Release Trains] section.
If you want to disable this check, just set the property [spring.cloud.compatibility-verifier.enabled=false]


2025-08-26 14:48:32 [main] WARN  c.a.n.client.logging.NacosLogging - Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
2025-08-26 14:48:32 [main] WARN  c.a.n.client.logging.NacosLogging - Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
2025-08-26 14:48:32 [main] INFO  c.g.a.s.GenerativeCardServiceApplication - Starting GenerativeCardServiceApplication using Java 17.0.12 with PID 32704 (D:\dev-center-dataservice\generative-card-service\target\classes started by ********** in D:\dev-center-dataservice\generative-card-service)
2025-08-26 14:48:32 [main] DEBUG c.g.a.s.GenerativeCardServiceApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-08-26 14:48:32 [main] INFO  c.g.a.s.GenerativeCardServiceApplication - The following 1 profile is active: "qa"
2025-08-26 14:48:34 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-26 14:48:34 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-26 14:48:34 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Redis repository interfaces.
2025-08-26 14:48:34 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=7e3f4abe-74e6-31f9-ad8d-1af55bb228f2
2025-08-26 14:48:36 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-26 14:48:36 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-26 14:48:36 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-26 14:48:36 [main] INFO  o.a.c.c.C.[.[.[/generative-card] - Initializing Spring embedded WebApplicationContext
2025-08-26 14:48:36 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3940 ms
2025-08-26 14:48:36 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-08-26 14:48:38 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 7 mappings in 'requestMappingHandlerMapping'
2025-08-26 14:48:38 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-08-26 14:48:38 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-08-26 14:48:38 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-08-26 14:48:39 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-26 14:48:42 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-26 14:48:42 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'compositeCompatibilityVerifier' defined in class path resource [org/springframework/cloud/configuration/CompatibilityVerifierAutoConfiguration.class]: Failed to instantiate [org.springframework.cloud.configuration.CompositeCompatibilityVerifier]: Factory method 'compositeCompatibilityVerifier' threw exception with message: Spring Cloud/ Spring Boot version compatibility checks have failed: [[VerificationResult@13e5b262 description = 'Spring Boot [3.2.0] is not compatible with this Spring Cloud release train', action = 'Change Spring Boot version to one of the following versions [3.0.x] .
You can find the latest Spring Boot versions here [https://spring.io/projects/spring-boot#learn]. 
If you want to learn more about the Spring Cloud Release train compatibility, you can visit this page [https://spring.io/projects/spring-cloud#overview] and check the [Release Trains] section.
If you want to disable this check, just set the property [spring.cloud.compatibility-verifier.enabled=false]']]
2025-08-26 14:48:42 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-26 14:48:42 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-26 14:48:42 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Your project setup is incompatible with our requirements due to following reasons:

- Spring Boot [3.2.0] is not compatible with this Spring Cloud release train


Action:

Consider applying the following actions:

- Change Spring Boot version to one of the following versions [3.0.x] .
You can find the latest Spring Boot versions here [https://spring.io/projects/spring-boot#learn]. 
If you want to learn more about the Spring Cloud Release train compatibility, you can visit this page [https://spring.io/projects/spring-cloud#overview] and check the [Release Trains] section.
If you want to disable this check, just set the property [spring.cloud.compatibility-verifier.enabled=false]


2025-08-26 15:32:05 [main] INFO  c.g.a.s.GenerativeCardServiceApplication - Starting GenerativeCardServiceApplication using Java 17.0.12 with PID 14080 (D:\dev-center-dataservice\generative-card-service\target\classes started by ********** in D:\dev-center-dataservice\generative-card-service)
2025-08-26 15:32:05 [main] DEBUG c.g.a.s.GenerativeCardServiceApplication - Running with Spring Boot v3.1.2, Spring v6.0.11
2025-08-26 15:32:05 [main] INFO  c.g.a.s.GenerativeCardServiceApplication - The following 1 profile is active: "qa"
2025-08-26 15:32:06 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-26 15:32:06 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-26 15:32:07 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-08-26 15:32:07 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c3e18a0-cc33-3434-961f-9367770f0bcf
2025-08-26 15:32:08 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-26 15:32:08 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-26 15:32:08 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-08-26 15:32:08 [main] INFO  o.a.c.c.C.[.[.[/generative-card] - Initializing Spring embedded WebApplicationContext
2025-08-26 15:32:08 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3469 ms
2025-08-26 15:32:08 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-08-26 15:32:10 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-08-26 15:32:10 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 7 mappings in 'requestMappingHandlerMapping'
2025-08-26 15:32:10 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-08-26 15:32:10 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-08-26 15:32:10 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'platformTransactionManagerCustomizers' defined in class path resource [org/springframework/boot/autoconfigure/transaction/TransactionManagerCustomizationAutoConfiguration.class]: Failed to instantiate [org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers]: Factory method 'platformTransactionManagerCustomizers' threw exception with message: 'org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers.of(java.util.Collection)'
2025-08-26 15:32:11 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-26 15:32:11 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-26 15:32:11 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

An attempt was made to call a method that does not exist. The attempt was made from the following location:

    org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizationAutoConfiguration.platformTransactionManagerCustomizers(TransactionManagerCustomizationAutoConfiguration.java:44)

The following method did not exist:

    'org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers.of(java.util.Collection)'

The calling method's class, org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizationAutoConfiguration, was loaded from the following location:

    jar:file:/D:/apache-maven-3.9.9/repository/org/springframework/boot/spring-boot-autoconfigure/3.2.10/spring-boot-autoconfigure-3.2.10.jar!/org/springframework/boot/autoconfigure/transaction/TransactionManagerCustomizationAutoConfiguration.class

The called method's class, org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers, is available from the following locations:

    jar:file:/D:/apache-maven-3.9.9/repository/org/springframework/boot/spring-boot-autoconfigure/3.1.2/spring-boot-autoconfigure-3.1.2.jar!/org/springframework/boot/autoconfigure/transaction/TransactionManagerCustomizers.class
    jar:file:/D:/apache-maven-3.9.9/repository/org/springframework/boot/spring-boot-autoconfigure/3.0.12/spring-boot-autoconfigure-3.0.12.jar!/org/springframework/boot/autoconfigure/transaction/TransactionManagerCustomizers.class
    jar:file:/D:/apache-maven-3.9.9/repository/org/springframework/boot/spring-boot-autoconfigure/3.2.10/spring-boot-autoconfigure-3.2.10.jar!/org/springframework/boot/autoconfigure/transaction/TransactionManagerCustomizers.class

The called method's class hierarchy was loaded from the following locations:

    org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers: file:/D:/apache-maven-3.9.9/repository/org/springframework/boot/spring-boot-autoconfigure/3.1.2/spring-boot-autoconfigure-3.1.2.jar


Action:

Correct the classpath of your application so that it contains compatible versions of the classes org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizationAutoConfiguration and org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers

