package com.gwm.ailab.service.service;

import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * HTTP服务类
 * 
 * <AUTHOR> Lab
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HttpService {

    private final OkHttpClient okHttpClient;

    @Value("${app.config.external-api.base-url:http://localhost:8080}")
    private String baseUrl;

    @Value("${app.config.external-api.retry-count:3}")
    private int retryCount;

    private static final MediaType JSON_MEDIA_TYPE = MediaType.get("application/json; charset=utf-8");

    /**
     * 发送GET请求
     */
    public String get(String path) throws IOException {
        return get(path, null);
    }

    /**
     * 发送GET请求（带参数）
     */
    public String get(String path, Map<String, String> params) throws IOException {
        HttpUrl.Builder urlBuilder = HttpUrl.parse(baseUrl + path).newBuilder();
        
        if (params != null) {
            params.forEach(urlBuilder::addQueryParameter);
        }

        Request request = new Request.Builder()
                .url(urlBuilder.build())
                .get()
                .build();

        return executeRequest(request);
    }

    /**
     * 发送POST请求
     */
    public String post(String path, Object body) throws IOException {
        String jsonBody = body instanceof String ? (String) body : JSON.toJSONString(body);
        
        RequestBody requestBody = RequestBody.create(jsonBody, JSON_MEDIA_TYPE);
        Request request = new Request.Builder()
                .url(baseUrl + path)
                .post(requestBody)
                .build();

        return executeRequest(request);
    }


    /**
     * 异步GET请求
     */
    public CompletableFuture<String> getAsync(String path) {
        return getAsync(path, null);
    }

    /**
     * 异步GET请求（带参数）
     */
    public CompletableFuture<String> getAsync(String path, Map<String, String> params) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return get(path, params);
            } catch (IOException e) {
                log.error("Async GET request failed: {}", e.getMessage(), e);
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 异步POST请求
     */
    public CompletableFuture<String> postAsync(String path, Object body) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return post(path, body);
            } catch (IOException e) {
                log.error("Async POST request failed: {}", e.getMessage(), e);
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 执行请求（带重试机制）
     */
    private String executeRequest(Request request) throws IOException {
        IOException lastException = null;
        
        for (int i = 0; i <= retryCount; i++) {
            try (Response response = okHttpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    ResponseBody responseBody = response.body();
                    return responseBody != null ? responseBody.string() : "";
                } else {
                    throw new IOException("HTTP " + response.code() + ": " + response.message());
                }
            } catch (IOException e) {
                lastException = e;
                if (i < retryCount) {
                    log.warn("Request failed, retrying... ({}/{}): {}", i + 1, retryCount, e.getMessage());
                    try {
                        Thread.sleep(1000 * (i + 1)); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("Request interrupted", ie);
                    }
                }
            }
        }
        
        throw new IOException("Request failed after " + retryCount + " retries", lastException);
    }
}
